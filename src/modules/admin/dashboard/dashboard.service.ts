import { Injectable } from '@nestjs/common';
import { BindRepo } from '~/@core/decorator';
import { OrderRepo } from '~/domains/primary/order/order.repo';
import { OrderItemRepo } from '~/domains/primary/order-item/order-item.repo';
import { PackagePlanRepo } from '~/domains/primary/package/package.repo';
import { Between, In, Raw } from 'typeorm';
import * as dayjs from 'dayjs';
import { PaymentTransactionRepo } from '~/domains/primary/payment-transaction/payment-transaction.repo';
import { NSOrder, NSPayment } from '~/common/enums';
import { MemberRepo } from '~/domains/primary';
import { MemberPackageRepo } from '~/domains/primary/member-package/member-package.repo';
import { Not } from 'typeorm';

@Injectable()
export class DashboardService {
  constructor() { }

  @BindRepo(OrderRepo)
  private orderRepo: OrderRepo;

  @BindRepo(OrderItemRepo)
  private orderItemRepo: OrderItemRepo;

  @BindRepo(PackagePlanRepo)
  private packagePlanRepo: PackagePlanRepo;

  @BindRepo(PaymentTransactionRepo)
  private paymentTransactionRepo: PaymentTransactionRepo;

  @BindRepo(MemberRepo)
  private memberRepo: MemberRepo;

  @BindRepo(MemberPackageRepo)
  private memberPackageRepo: MemberPackageRepo;

  //Tổng số lượng khách hàng
  async getTotalCustomer() {
    const totalCustomer = await this.memberRepo.count();
    return { total: totalCustomer };
  }

  //Tổng số lượng đơn hàng
  async getTotalOrder() {
    const totalOrder = await this.orderRepo.count({
      where: { paymentStatus: NSOrder.EPaymentStatus.PAID },
    });
    return { total: totalOrder };
  }

  //Tổng doanh thu
  async getTotalRevenue() {
    const totalRevenue: any = await this.orderRepo.sum({
      sumSelect: ['totalPriceVat'],
      where: { paymentStatus: NSOrder.EPaymentStatus.PAID },
    });
    return { total: totalRevenue.totalPriceVat };
  }

  //Thống kê số lượng khách hàng theo tháng
  async getCustomerStats(year: number = dayjs().year(), month?: number) {
    let start: Date, end: Date;
    let labels: string[] = [];
    let memberCounts: number[] = [];
    let memberBuyCounts: number[] = [];

    // Kiểm tra month có hợp lệ không (phải là số nguyên từ 1 đến 12)
    const isMonthValid =
      Number.isInteger(month) && month! >= 1 && month! <= 12;

    if (isMonthValid) {
      // Nếu có month, tính theo ngày trong tháng
      const startDay = dayjs(`${year}-${month}-01`).startOf('month');
      const endDay = startDay.endOf('month');
      start = startDay.toDate();
      end = endDay.toDate();

      const daysInMonth = startDay.daysInMonth(); // Số ngày trong tháng

      labels = Array.from({ length: daysInMonth }, (_, i) =>
        (i + 1).toString().padStart(2, '0')
      );
      memberCounts = Array(daysInMonth).fill(0);
      memberBuyCounts = Array(daysInMonth).fill(0);
    } else {
      // Nếu không có month, tính theo tháng trong năm
      start = dayjs(`${year}-01-01`).startOf('month').toDate();
      end = dayjs(`${year}-12-31`).endOf('month').toDate();

      labels = Array.from({ length: 12 }, (_, i) => `Tháng ${i + 1}`);
      memberCounts = Array(12).fill(0);
      memberBuyCounts = Array(12).fill(0);
    }

    const members = await this.memberRepo.find({
      where: {
        createdDate: Between(start, end),
      },
      select: ['id', 'createdDate'],
    });

    const member_buy = await this.memberPackageRepo.find({
      where: {
        activatedDate: Between(start, end),
      },
      select: ['memberId', 'activatedDate'],
    });

    const uniqueMemberBuySet = new Set<string>();

    for (const member of members) {
      const time = dayjs(member.createdDate);
      const index =  isMonthValid ? time.date() - 1 : time.month(); // date() = ngày trong tháng
      memberCounts[index]++;
    }

    for (const record of member_buy) {
      const time = dayjs(record.activatedDate);
      const index = isMonthValid ? time.date() - 1 : time.month();
      if (!uniqueMemberBuySet.has(record.memberId)) {
        uniqueMemberBuySet.add(record.memberId);
        memberBuyCounts[index]++;
      }
    }

    const datasets = [
      {
        label: isMonthValid ? 'Khách đăng ký theo ngày' : 'Khách đăng ký theo tháng',
        data: memberCounts,
        borderColor: '#3b82f6',
        backgroundColor: '#3b82f6',
        fill: false,
      },
      {
        label: isMonthValid ? 'Khách mua gói theo ngày' : 'Khách mua gói theo tháng',
        data: memberBuyCounts,
        borderColor: '#10b981',
        backgroundColor: '#10b981',
        fill: false,
      },
    ];

    return { labels, datasets };
  }

  //thống kê số lượng đơn hàng theo tháng, năm
  async getOrderStats(year: number = dayjs().year(), month?: number) {
    let start: Date, end: Date;
    let labels: string[] = [];
    let paidCounts: number[] = [];
    let unpaidCounts: number[] = [];

    // Kiểm tra month có hợp lệ không (phải là số nguyên từ 1 đến 12)
    const isMonthValid =
      Number.isInteger(month) && month! >= 1 && month! <= 12;

    if (isMonthValid) {
      // Trường hợp có tháng → thống kê theo ngày trong tháng
      const startDay = dayjs(`${year}-${month}-01`).startOf('month');
      const endDay = startDay.endOf('month');
      const daysInMonth = startDay.daysInMonth();

      start = startDay.toDate();
      end = endDay.toDate();

      labels = Array.from({ length: daysInMonth }, (_, i) =>
        (i + 1).toString().padStart(2, '0')
      );
      paidCounts = Array(daysInMonth).fill(0);
      unpaidCounts = Array(daysInMonth).fill(0);
    } else {
      // Trường hợp không có tháng → thống kê theo tháng trong năm
      start = dayjs(`${year}-01-01`).startOf('month').toDate();
      end = dayjs(`${year}-12-31`).endOf('month').toDate();

      labels = Array.from({ length: 12 }, (_, i) => `Tháng ${i + 1}`);
      paidCounts = Array(12).fill(0);
      unpaidCounts = Array(12).fill(0);
    }

    // Truy vấn đơn hàng
    const paidOrders = await this.orderRepo.find({
      where: {
        status: NSOrder.EStatus.COMPLETED,
        createdDate: Between(start, end),
      },
      select: ['id', 'createdDate'],
    });

    const unpaidOrders = await this.orderRepo.find({
      where: {
        status: Not(NSOrder.EStatus.COMPLETED),
        createdDate: Between(start, end),
      },
      select: ['id', 'createdDate'],
    });

    for (const order of paidOrders) {
      const time = dayjs(order.createdDate);
      const index = isMonthValid ? time.date() - 1 : time.month(); // 0-based
      paidCounts[index]++;
    }

    for (const order of unpaidOrders) {
      const time = dayjs(order.createdDate);
      const index = isMonthValid ? time.date() - 1 : time.month();
      unpaidCounts[index]++;
    }

    const datasets = [
      {
        label: isMonthValid
          ? 'Đơn đã thanh toán theo ngày'
          : 'Đơn đã thanh toán theo tháng',
        data: paidCounts,
        borderColor: '#1336e7ff',
        backgroundColor: '#1336e7ff',
      },
      {
        label: isMonthValid
          ? 'Đơn chưa thanh toán theo ngày'
          : 'Đơn chưa thanh toán theo tháng',
        data: unpaidCounts,
        borderColor: '#f4e163ff',
        backgroundColor: '#f4e163ff',
      },
    ];

    return { labels, datasets };
  }

  // Thống kê gói dịch vụ trong 1 năm ( tháng 1 -> tháng 12 )
  async getPackageStatisticsChart(year: number = dayjs().year()) {
    const orders = await this.orderRepo.find({
      where: {
        paymentStatus: 'PAID',
        paymentDate: Raw(alias => `${alias} >= :start AND ${alias} <= :end`, {
          start: `${year}-01-01T00:00:00Z`,
          end: `${year}-12-31T23:59:59Z`,
        }),
      },
      select: ['id', 'paymentDate'],
    });

    if (!orders.length) {
      return this.generateEmptyChart();
    }

    const orderIds = orders.map(o => o.id);

    const orderItems = await this.orderItemRepo.find({
      where: { orderId: In(orderIds) },
      select: ['orderId', 'packagePlanId', 'quantity'],
    });

    const orderIdToMonth = new Map(
      orders.map(o => [o.id, dayjs(o.paymentDate).month()]), // 0-11
    );

    const monthlyCountMap = new Map<number, Map<string, number>>();
    for (let i = 0; i < 12; i++) monthlyCountMap.set(i, new Map());

    const totalCountMap = new Map<string, number>(); // dùng cho pie chart

    for (const item of orderItems) {
      const month = orderIdToMonth.get(item.orderId);
      if (month === undefined) continue;

      // tháng
      const monthMap = monthlyCountMap.get(month)!;
      monthMap.set(item.packagePlanId, (monthMap.get(item.packagePlanId) || 0) + item.quantity);

      // năm
      totalCountMap.set(
        item.packagePlanId,
        (totalCountMap.get(item.packagePlanId) || 0) + item.quantity,
      );
    }

    const packageIds = Array.from(new Set(orderItems.map(i => i.packagePlanId)));
    const packages = await this.packagePlanRepo.findByIds(packageIds);
    const packageMap = new Map(packages.map(p => [p.id, p.name]));

    // ✅ Dữ liệu biểu đồ cột
    const monthLabels = Array.from({ length: 12 }, (_, i) => `Tháng ${i + 1}`);
    const packageIdToLabel = new Map<string, string>();
    for (const pkg of packages) {
      packageIdToLabel.set(pkg.id, pkg.name);
    }

    const colorPalette = this.generateColorPalette(packages.length);
    const pkgToColor = new Map<string, string>();
    packages.forEach((p, i) => pkgToColor.set(p.id, colorPalette[i]));

    const datasetMap = new Map<string, number[]>(); // packagePlanId => [12 tháng]
    for (const pkgId of packageIds) {
      datasetMap.set(pkgId, Array(12).fill(0));
    }

    for (let month = 0; month < 12; month++) {
      const monthMap = monthlyCountMap.get(month)!;
      for (const [pkgId, count] of monthMap.entries()) {
        datasetMap.get(pkgId)![month] = count;
      }
    }

    const barChart = {
      labels: monthLabels,
      datasets: Array.from(datasetMap.entries()).map(([pkgId, data]) => ({
        label: packageMap.get(pkgId) || 'Unknown',
        data,
        backgroundColor: pkgToColor.get(pkgId),
      })),
    };

    // ✅ Dữ liệu biểu đồ tròn (pie), phần trăm
    const sumTotal = Array.from(totalCountMap.values()).reduce((acc, cur) => acc + cur, 0);
    const percentage = Array.from(totalCountMap.values()).map(count =>
      parseFloat(((count / sumTotal) * 100).toFixed(2)),
    );
    const pieChart = {
      labels: Array.from(totalCountMap.keys()).map(pkgId => packageMap.get(pkgId) || 'Unknown'),
      datasets: [
        {
          label: 'Số lượng gói dịch vụ (%)',
          data: percentage,
          backgroundColor: Array.from(totalCountMap.keys()).map(pkgId => pkgToColor.get(pkgId)!),
        },
      ],
    };

    return { barChart, pieChart };
  }

  // Thống kê phương thức thanh toán /giao dịch trong 1 năm ( tháng 1 -> tháng 12 )
  async getPaymentGatewayStats(year: number = dayjs().year()) {
    const start = dayjs(`${year}-01-01`).startOf('month').toDate();
    const end = dayjs(`${year}-12-31`).endOf('month').toDate();

    const transactions = await this.paymentTransactionRepo.find({
      where: {
        status: NSPayment.ETransactionStatus.COMPLETED,
        transactionDate: Raw(alias => `${alias} BETWEEN :start AND :end`, { start, end }),
      },
      select: ['id', 'paymentProvider', 'transactionDate'],
    });

    const methodMap = new Map<string, number[]>();

    for (const tx of transactions) {
      const method = tx.paymentProvider || 'unknown';
      const month = dayjs(tx.transactionDate).month(); // 0-based (0 = Jan)

      if (!methodMap.has(method)) {
        methodMap.set(method, Array(12).fill(0));
      }
      methodMap.get(method)![month] += 1;
    }

    const labels = Array.from({ length: 12 }, (_, i) => `Tháng ${i + 1}`);
    const colorPalette = this.generateColorPalette(methodMap.size);

    const datasets = Array.from(methodMap.entries()).map(([method, data], index) => ({
      label: method,
      data,
      backgroundColor: colorPalette[index],
    }));

    return { labels, datasets };
  }

  // Thống kế doanh thu trong 1 năm ( tháng 1 -> tháng 12 )
  async getRevenueStats(year: number = dayjs().year(), month?: number) {
    // Xác định xem tháng có hợp lệ không
    const isMonthValid = Number.isInteger(month) && month! >= 1 && month! <= 12;

    let start: Date, end: Date;
    let labels: string[] = [];
    let revenueMap: Map<number, number>;

    if (isMonthValid) {
      const startDay = dayjs(`${year}-${month}-01`).startOf('month');
      const endDay = startDay.endOf('month');
      const daysInMonth = startDay.daysInMonth();

      start = startDay.toDate();
      end = endDay.toDate();

      labels = Array.from({ length: daysInMonth }, (_, i) =>
        (i + 1).toString().padStart(2, '0')
      );
      revenueMap = new Map(Array.from({ length: daysInMonth }, (_, i) => [i, 0]));
    } else {
      start = dayjs(`${year}-01-01`).startOf('month').toDate();
      end = dayjs(`${year}-12-31`).endOf('month').toDate();

      labels = Array.from({ length: 12 }, (_, i) => `Tháng ${i + 1}`);
      revenueMap = new Map(Array.from({ length: 12 }, (_, i) => [i, 0]));
    }

    // Truy vấn giao dịch
    const transactions = await this.paymentTransactionRepo.find({
      where: {
        status: NSPayment.ETransactionStatus.COMPLETED,
        transactionDate: Raw(alias => `${alias} BETWEEN :start AND :end`, { start, end }),
      },
      select: ['id', 'amount', 'transactionDate'],
    });

    for (const tx of transactions) {
      const time = dayjs(tx.transactionDate);
      const index = isMonthValid ? time.date() - 1 : time.month(); // 0-based
      revenueMap.set(index, revenueMap.get(index)! + +tx.amount);
    }

    const data = Array.from(revenueMap.values());

    return {
      labels,
      datasets: [
        {
          label: isMonthValid ? 'Doanh thu theo ngày ($)' : 'Doanh thu theo tháng ($)',
          data,
          borderColor: '#3b82f6',
          backgroundColor: '#3b82f6',
          fill: false,
        },
      ],
    };
  }

  // Thống kê doanh thu theo gói dịch vụ
  async getRevenueByPackage(year: number, month?: number) {
    const start = month
      ? dayjs(`${year}-${month}-01`).startOf('month').toDate()
      : dayjs(`${year}-01-01`).startOf('year').toDate();

    const end = month
      ? dayjs(`${year}-${month}-01`).endOf('month').toDate()
      : dayjs(`${year}-12-31`).endOf('year').toDate();

    const transactions = await this.paymentTransactionRepo.find({
      where: {
        status: 'COMPLETED',
        transactionDate: Between(start, end),
      },
    });

    const packageRevenueMap = new Map<string, { name: string; total: number; timeSeries: Map<string, number> }>();

    for (const tx of transactions) {
      const orderId = tx.orderId;
      if (!orderId) continue;

      const txDate = dayjs(tx.transactionDate);
      const timeKey = month
        ? txDate.format('DD') // từng ngày nếu có month
        : txDate.format('MM'); // từng tháng nếu chỉ có year

      // Lấy order items từ orderId
      const orderItems = await this.orderItemRepo.find({ where: { orderId } });

      for (const item of orderItems) {
        const pkg = await this.packagePlanRepo.findOne({ where: { id: item.packagePlanId } });
        if (!pkg) continue;

        const existing = packageRevenueMap.get(pkg.id) ?? {
          name: pkg.name,
          total: 0,
          timeSeries: new Map<string, number>(),
        };

        existing.total += +tx.amount;

        const current = existing.timeSeries.get(timeKey) ?? 0;
        existing.timeSeries.set(timeKey, current + +tx.amount);

        packageRevenueMap.set(pkg.id, existing);
      }
    }

    // Chuẩn hóa dữ liệu cho Chart
    const pieChart = [];
    let totalRevenue = 0;

    for (const [, value] of packageRevenueMap) {
      totalRevenue += value.total;
    }

    for (const [, value] of packageRevenueMap) {
      pieChart.push({
        name: value.name,
        total: +(+value.total).toFixed(2),
        percent: +(+value.total / +totalRevenue * 100).toFixed(2),
      });
    }

    const labels = month
      ? Array.from({ length: dayjs(`${year}-${month}`).daysInMonth() }, (_, i) => `${i + 1}`)
      : Array.from({ length: 12 }, (_, i) => `${i + 1}`);

    const lineChart = {
      labels,
      datasets: [],
    };

    for (const [, value] of packageRevenueMap) {
      const data = labels.map(label => +((value.timeSeries.get(label) ?? 0)).toFixed(2));
      lineChart.datasets.push({
        label: value.name,
        data,
      });
    }

    return {
      lineChart,
      pieChart,
    };
  }


  private generateEmptyChart() {
    const emptyLabels = Array.from({ length: 12 }, (_, i) => `Tháng ${i + 1}`);
    return {
      barChart: {
        labels: emptyLabels,
        datasets: [],
      },
      pieChart: {
        labels: [],
        data: [],
        percentage: [],
        backgroundColor: [],
      },
    };
  }

  // TODO 0. Thống kê số lượng giao dịch mua/gia hạn gói dịch vụ trong 1 năm ( tháng 1 -> tháng 12 )
  async getTransactionStats(year: number = dayjs().year()) {
    const start = dayjs(`${year}-01-01`).startOf('month').toDate();
    const end = dayjs(`${year}-12-31`).endOf('month').toDate();

    const transactions = await this.paymentTransactionRepo.find({
      where: {
        status: NSPayment.ETransactionStatus.COMPLETED,
        transactionDate: Raw(alias => `${alias} BETWEEN :start AND :end`, { start, end }),
      },
      select: ['id', 'transactionType', 'transactionDate'],
    });

    const monthMap = new Map<number, Map<NSPayment.ETransactionType, number>>();
    for (let i = 0; i < 12; i++) {
      monthMap.set(i, new Map());
    }

    for (const tx of transactions) {
      const month = dayjs(tx.transactionDate).month(); // 0-based (0 = Jan)
      const monthMapForMonth = monthMap.get(month)!;
      monthMapForMonth.set(tx.transactionType, (monthMapForMonth.get(tx.transactionType) || 0) + 1);
    }

    const labels = Array.from({ length: 12 }, (_, i) => `Tháng ${i + 1}`);
    const colorPalette = this.generateColorPalette(2);
    const datasets = [
      {
        label: 'Mua gói dịch vụ',
        data: Array.from(monthMap.values()).map(
          monthMapForMonth => monthMapForMonth.get(NSPayment.ETransactionType.PAYMENT) || 0,
        ),
        backgroundColor: colorPalette[0],
      },
      {
        label: 'Gia hạn gói dịch vụ',
        data: Array.from(monthMap.values()).map(
          monthMapForMonth => monthMapForMonth.get(NSPayment.ETransactionType.RENEWAL) || 0,
        ),
        backgroundColor: colorPalette[1],
      },
    ];

    return { labels, datasets };
  }

  // Thống kê số lượng giao dịch theo tháng, param is month
  async getTransactionStatsByMonth(month: number = dayjs().month()) {
    const start = dayjs().month(month).startOf('month').toDate();
    const end = dayjs().month(month).endOf('month').toDate();

    const transactions = await this.paymentTransactionRepo.find({
      where: {
        status: NSPayment.ETransactionStatus.COMPLETED,
        transactionDate: Raw(alias => `${alias} BETWEEN :start AND :end`, { start, end }),
      },
      select: ['id', 'transactionType', 'transactionDate'],
    });

    const dayMap = new Map<number, Map<NSPayment.ETransactionType, number>>();
    for (let i = 0; i < dayjs(end).date(); i++) {
      dayMap.set(i, new Map());
    }

    for (const tx of transactions) {
      const day = dayjs(tx.transactionDate).date() - 1; // 1-based (1 = Jan)
      const dayMapForDay = dayMap.get(day)!;
      dayMapForDay.set(tx.transactionType, (dayMapForDay.get(tx.transactionType) || 0) + 1);
    }

    const labels = Array.from({ length: dayjs(end).date() }, (_, i) => `Ngày ${i + 1}`);
    const colorPalette = this.generateColorPalette(2);
    const datasets = [
      {
        label: 'Mua gói dịch vụ',
        data: Array.from(dayMap.values()).map(
          dayMapForDay => dayMapForDay.get(NSPayment.ETransactionType.PAYMENT) || 0,
        ),
        backgroundColor: colorPalette[0],
      },
      {
        label: 'Gia hạn gói dịch vụ',
        data: Array.from(dayMap.values()).map(
          dayMapForDay => dayMapForDay.get(NSPayment.ETransactionType.RENEWAL) || 0,
        ),
        backgroundColor: colorPalette[1],
      },
    ];

    return { labels, datasets };
  }

  // TODO 1. Thông kê chi phí gas onchain trong 1 năm ( tháng 1 -> tháng 12 ).
  // TODO Gán tạm edit sau.
  async getGasStats(year: number = dayjs().year()) {
    const start = dayjs(`${year}-01-01`).startOf('month').toDate();
    const end = dayjs(`${year}-12-31`).endOf('month').toDate();

    const transactions = await this.paymentTransactionRepo.find({
      where: {
        status: NSPayment.ETransactionStatus.COMPLETED,
        transactionDate: Raw(alias => `${alias} BETWEEN :start AND :end`, { start, end }),
      },
      select: ['id', 'transactionType', 'transactionDate'],
    });

    const monthMap = new Map<number, Map<NSPayment.ETransactionType, number>>();
    for (let i = 0; i < 12; i++) {
      monthMap.set(i, new Map());
    }

    for (const tx of transactions) {
      const month = dayjs(tx.transactionDate).month(); // 0-based (0 = Jan)
      const monthMapForMonth = monthMap.get(month)!;
      monthMapForMonth.set(tx.transactionType, (monthMapForMonth.get(tx.transactionType) || 0) + 1);
    }

    const labels = Array.from({ length: 12 }, (_, i) => `Tháng ${i + 1}`);
    const colorPalette = this.generateColorPalette(1);
    const datasets = [
      {
        label: 'Chi phí gas onchain',
        data: Array.from(monthMap.values()).map(
          monthMapForMonth => monthMapForMonth.get(NSPayment.ETransactionType.PAYMENT) || 0,
        ),
        backgroundColor: colorPalette[0],
      },
    ];

    return { labels, datasets };
  }

  async getOnChainTransactionStats(year: number = dayjs().year()) {
    const start = dayjs(`${year}-01-01`).startOf('month').toDate();
    const end = dayjs(`${year}-12-31`).endOf('month').toDate();

    const transactions = await this.paymentTransactionRepo.find({
      where: {
        status: NSPayment.ETransactionStatus.COMPLETED,
        transactionDate: Raw(alias => `${alias} BETWEEN :start AND :end`, { start, end }),
      },
      select: ['id', 'transactionType', 'transactionDate'],
    });

    const monthMap = new Map<number, Map<NSPayment.ETransactionType, number>>();
    for (let i = 0; i < 12; i++) {
      monthMap.set(i, new Map());
    }

    for (const tx of transactions) {
      const month = dayjs(tx.transactionDate).month(); // 0-based (0 = Jan)
      const monthMapForMonth = monthMap.get(month)!;
      monthMapForMonth.set(tx.transactionType, (monthMapForMonth.get(tx.transactionType) || 0) + 1);
    }

    const labels = Array.from({ length: 12 }, (_, i) => `Tháng ${i + 1}`);
    const colorPalette = this.generateColorPalette(1);
    const datasets = [
      {
        label: 'Số lượng giao dịch onchain',
        data: Array.from(monthMap.values()).map(
          monthMapForMonth => monthMapForMonth.get(NSPayment.ETransactionType.PAYMENT) || 0,
        ),
        backgroundColor: colorPalette[0],
      },
    ];

    return { labels, datasets };
  }

  private generateColorPalette(count: number): string[] {
    const baseColors = [
      '#FF6384',
      '#36A2EB',
      '#FFCE56',
      '#4BC0C0',
      '#9966FF',
      '#FF9F40',
      '#00A36C',
      '#D2691E',
      '#8A2BE2',
      '#FF4500',
      '#6A5ACD',
      '#3CB371',
      '#B22222',
      '#2E8B57',
      '#FFD700',
    ];
    const result: string[] = [];
    for (let i = 0; i < count; i++) {
      result.push(baseColors[i % baseColors.length]);
    }
    return result;
  }
}
