import { Injectable } from '@nestjs/common';
import { CreatePackagePlanDto, UpdatePackagePlanDto, PackagePlanListDto } from './dto/package.dto';
import { BindRepo, DefTransaction } from '~/@core/decorator';
import { PackagePlanRepo } from '~/domains/primary/package/package.repo';
import { NSPackage } from '~/common/enums/package.enum';
import { Between } from 'typeorm';
import { generateCodeHelper } from '~/common/helpers/generate-code.helper';
import Stripe from 'stripe';
import { configEnv } from '~/@config/env';
import { BusinessException } from '~/@systems/exceptions';
import { PackagePlanDetailRepo } from '~/domains/primary';

@Injectable()
export class PackageService {
  private stripe: Stripe
  constructor() {
    this.stripe = new Stripe(configEnv().STRIPE_SECRET_KEY, {});
  }

  @BindRepo(PackagePlanRepo)
  private packagePlanRepo: PackagePlanRepo;

  @BindRepo(PackagePlanDetailRepo)
  private PackagePlanDetailRepo: PackagePlanDetailRepo;

  @DefTransaction()
  async create(dto: CreatePackagePlanDto) {
    const count = await this.packagePlanRepo.count();
    const planCode = generateCodeHelper.generateCode('APE', count + 1);
    const pkg = this.packagePlanRepo.create({
      ...dto,
      code: planCode,
    });

    // Nếu là gói dùng thử thì set giá bằng 0 hết
    const { STRIPE_PRODUCT_SUBSCRIPTION_ID } = configEnv();
    const productId = STRIPE_PRODUCT_SUBSCRIPTION_ID; // product chung cho các gói

    const newPackage = await this.packagePlanRepo.save(pkg);
    const { details } = dto;
    if (details) {
      for (const detail of details) {
        if (detail.unitPrice > 0) {
          const price = await this.stripe.prices.create({
            unit_amount: +detail.unitPrice * 100,
            currency: "usd",
            recurring: {
              interval: 'month',
              interval_count: detail.billingCycle, // Số lượng tháng
            },
            product: productId,
          });
          detail.stripePriceId = price.id;
        }
        detail.packagePlanId = newPackage.id;
        detail.totalPrice = +detail.unitPrice * +detail.billingCycle;
        detail.discountPercent = Math.round(
          ((newPackage.originalPrice - detail.unitPrice) / newPackage.originalPrice) * 100,
        );
        // Tạo detail
        await this.PackagePlanDetailRepo.save(detail);
      }
    }
    return newPackage;
  }

  async findAll() {
    return this.packagePlanRepo.find();
  }

  async findPagination(body: PackagePlanListDto) {
    const { code, memberId, name, status, createdDateFrom, createdDateTo, ...pageRequest } = body;
    const where: any = {};
    if (name) {
      where.name = name;
    }
    //code
    if (code) {
      where.code = code;
    }
    //memberId
    if (memberId) {
      where.memberId = memberId;
    }
    if (status) {
      where.status = status;
    }
    if (createdDateFrom && createdDateTo) {
      where.createdDate = Between(createdDateFrom, createdDateTo);
    }
    return await this.packagePlanRepo.findPagination({ where }, pageRequest);
  }

  async findOne(id: string) {
    const pkg = await this.packagePlanRepo.findOne({ where: { id } });
    if (!pkg) throw new BusinessException('package.not_found');
    const details = await this.PackagePlanDetailRepo.find({ where: { packagePlanId: pkg.id } });
    return {
      ...pkg,
      details,
    };
  }

  @DefTransaction()
  async update(body: UpdatePackagePlanDto) {
    const { id, ...dto } = body;
    const pkg = await this.findOne(id);
    if (!pkg) throw new BusinessException('package.not_found');
    const currentDetails = await this.PackagePlanDetailRepo.find({ where: { packagePlanId: id } });

    const { STRIPE_PRODUCT_SUBSCRIPTION_ID } = configEnv();
    const productId = STRIPE_PRODUCT_SUBSCRIPTION_ID; // product chung cho các gói

    if(dto.details) {
      for (const detail of dto.details) {
        if (detail.unitPrice > 0) {
          const currentPrice = currentDetails.find((p) => p.billingCycle === detail.billingCycle && p.id == detail.id);
          if (currentPrice && currentPrice.stripePriceId && currentPrice.totalPrice !== detail.totalPrice) {
            await this.stripe.prices.update(currentPrice.stripePriceId, {
              active: false,
            });
          }
          const totalPrice = +detail.unitPrice * +detail.billingCycle;
          const price = await this.stripe.prices.create({
            unit_amount: +totalPrice * 100,
            currency: "usd",
            recurring: {
              interval: 'month',
              interval_count: detail.billingCycle, // Số lượng tháng
            },
            product: productId,
          });
          detail.stripePriceId = price.id;
          detail.totalPrice = totalPrice;
          detail.packagePlanId = id;
        }
        // Tạo detail
        await this.PackagePlanDetailRepo.save(detail);
      }
    }

    delete dto.details
    return this.packagePlanRepo.update(
      {
        id,
      },
      {
        ...dto,
      },
    );
  }

  @DefTransaction()
  async inActive(id: string) {
    const pkg = await this.findOne(id);
    if (!pkg) throw new BusinessException('package.not_found');
    return await this.packagePlanRepo.update(
      {
        id,
      },
      {
        status: NSPackage.EStatus.INACTIVE,
      },
    );
  }

  @DefTransaction()
  async active(id: string) {
    const pkg = await this.findOne(id);
    if (!pkg) throw new BusinessException('package.not_found');
    return await this.packagePlanRepo.update(
      {
        id,
      },
      {
        status: NSPackage.EStatus.ACTIVE,
      },
    );
  }
}
