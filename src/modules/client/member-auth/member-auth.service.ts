import { Injectable } from '@nestjs/common';
import { MemberLoginReq, MemberRegisterReq, MemberUpdateInfoDto } from './dto';
import { BindRepo } from '~/@core/decorator';
import { MemberKeyRepo, MemberRepo } from '~/domains/primary';
import { BusinessException } from '~/@systems/exceptions';
import securityHelper from '~/@core/helpers/security.helper';
import { MemberEntity } from '~/domains/primary';
import { JwtService } from '@nestjs/jwt';
import { configEnv } from '~/@config/env';
import { DefTransaction } from '~/@core/decorator';
import { NSMember } from '~/common/enums';
import { MemberAuthProviderRepo } from '~/domains/primary';
import { memberSessionContext } from '../member-session.context';
import { LessThan, MoreThan } from 'typeorm';
import { TwoFactorService } from '../two-factor/two-factor.service';
import { SuccessResponse } from '~/@systems/utils';
@Injectable()
export class MemberAuthService {
    constructor(
        private jwtService: JwtService,
        private twoFactorService: TwoFactorService, // Thêm dependency
    ) {}
    @BindRepo(MemberRepo)
    private memberRepo: MemberRepo;

    @BindRepo(MemberAuthProviderRepo)
    private memberAuthProviderRepo: MemberAuthProviderRepo;

    @BindRepo(MemberKeyRepo)
    private memberKeyRepo: MemberKeyRepo;

    private async generateRefreshToken(memberId: string) {
        const { JWT_REFRESH_TOKEN_EXPIRY, JWT_REFRESH_TOKEN_SECRET } = configEnv();
        const newRefreshToken = await this.jwtService.signAsync(
            { sub: memberId },
            {
                secret: JWT_REFRESH_TOKEN_SECRET,
                expiresIn: JWT_REFRESH_TOKEN_EXPIRY,
            },
        );

        return newRefreshToken;
    }

    private clearPrivateMemberData(member: MemberEntity) {
        const { password, createdBy, updatedBy, createdDate, updatedDate, ...rest } = member;
        return rest;
    }

    private async makeAuthResponse(member: MemberEntity) {
        const pipeMember = this.clearPrivateMemberData(member);
        const payload = {
            sub: member.id,
            ...pipeMember,
        };
        return {
            accessToken: await this.jwtService.signAsync(payload),
            refreshToken: await this.generateRefreshToken(member.id),
            tokenType: 'Bearer',
            ...pipeMember,
        };
    }

    @DefTransaction()
    async register(body: MemberRegisterReq) {
        const member = await this.memberRepo.findOne({
            where: {
                email: body.email,
            },
        });
        if (member) {
            const memberAuthProvider = await this.memberAuthProviderRepo.findOne({
                where: {
                    provider: NSMember.EAuthProviderType.EMAIL,
                    providerId: member.id,
                },
            });
            if (memberAuthProvider) {
                throw new BusinessException('member_auth.register.error.member_existed');
            }
            await this.memberAuthProviderRepo.save({
                memberId: member.id,
                provider: NSMember.EAuthProviderType.EMAIL,
                providerId: member.id,
            });
            await this.memberRepo.update(
                { id: member.id },
                { password: await securityHelper.hash(body.password) },
            );
            return this.makeAuthResponse(member);
        }
        const newMember = await this.memberRepo.save({
            email: body.email,
            password: await securityHelper.hash(body.password),
            fullName: body.fullName,
        });
        await this.memberAuthProviderRepo.save({
            memberId: newMember.id,
            provider: NSMember.EAuthProviderType.EMAIL,
            providerId: newMember.id,
        });

        return this.makeAuthResponse(newMember);
    }

    async login(body: MemberLoginReq) {
        const member = await this.memberRepo.findOne({
            where: {
                email: body.email,
            },
        });

        if (!member) {
            throw new BusinessException('member_auth.login.error.member_not_existed');
        }

        if (member.status === NSMember.EStatus.LOCKED) {
            throw new BusinessException('member_auth.login.error.locked');
        }

        const provider = await this.memberAuthProviderRepo.findOne({
            where: {
                provider: NSMember.EAuthProviderType.EMAIL,
                providerId: member.id,
            },
        });
        if (!provider) {
            throw new BusinessException('member_auth.login.error.wrong_provider');
        }
        const checkPass = await securityHelper.compare(body?.password, member.password);
        if (!checkPass) {
            throw new BusinessException('member_auth.login.error.wrong_password');
        }

        return this.makeAuthResponse(member);
    }

    async loginWith2FA(memberId: string, code: string) {
        const member = await this.memberRepo.findOne({
            where: { id: memberId },
        });

        if (!member) {
            throw new BusinessException('member_auth.login.error.member_not_existed');
        }

        if (!member.twoFactorEnabled) {
            throw new BusinessException('2fa.not_enabled');
        }

        // Xác thực 2FA code
        const isValid = await this.twoFactorService.verify2FA(memberId, code);
        if (!isValid) {
            throw new BusinessException('2fa.invalid_code');
        }

        return this.makeAuthResponse(member);
    }

    async googleLogin(googleProfile: {
        email: string;
        providerId: string;
        fullName?: string;
        avatar?: string;
    }) {
        const { email, providerId, fullName, avatar } = googleProfile;
        let member = await this.memberRepo.findOne({
            where: {
                email,
            },
        });
        if (!member) {
            member = await this.memberRepo.save({
                email,
                fullName,
                avatar,
            });
            await this.memberAuthProviderRepo.save({
                memberId: member.id,
                provider: NSMember.EAuthProviderType.GOOGLE,
                providerId,
            });
            return this.makeAuthResponse(member);
        }
        if (member.status === NSMember.EStatus.LOCKED) {
            throw new BusinessException('member_auth.login.error.locked');
        }
        // Tìm xem đã liên kết với provider chưa
        const provider = await this.memberAuthProviderRepo.findOne({
            where: {
                provider: NSMember.EAuthProviderType.GOOGLE,
                providerId,
            },
        });
        if (!provider) {
            await this.memberAuthProviderRepo.save({
                memberId: member.id,
                provider: NSMember.EAuthProviderType.GOOGLE,
                providerId,
            });
            return this.makeAuthResponse(member);
        }
        return this.makeAuthResponse(member);
    }

    /**
     * Cập nhật thông tin của member
     * @param body
     * @returns
     */
    async update(body: MemberUpdateInfoDto) {
        const { memberId } = memberSessionContext;
        const member = await this.memberRepo.findOneOrFail({ where: { id: memberId } });
        if (!member) throw new BusinessException('member_not_existed');
        await this.memberRepo.update({ id: member.id }, { ...body });
        return new SuccessResponse();
    }

    async me(memberId: string) {
        const member = await this.memberRepo.findOne({ where: { id: memberId } });

        if (!member) throw new BusinessException('member_not_existed');

        // lấy key mới nhất
        const memberKey = await this.memberKeyRepo.findOne({
            where: { memberId },
            order: { createdDate: 'DESC' },
        });
        return {
            ...(await this.makeAuthResponse(member)),
            ...memberKey,
        };
    }
}
