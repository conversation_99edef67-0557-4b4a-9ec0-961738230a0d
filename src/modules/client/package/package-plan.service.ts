import { Injectable, } from '@nestjs/common';
import { PackagePlanListDto } from './dto/package.dto';
import { BindRepo, } from '~/@core/decorator';
import { PackagePlanRepo } from '~/domains/primary/package/package.repo';
import { NSPackage } from '~/common/enums/package.enum';
import { PackagePlanDetailRepo } from '~/domains/primary/package-plan-detail/package-plan-detail.repo';

@Injectable()
export class PackagePlanService {
  constructor() {
  }

  @BindRepo(PackagePlanRepo)
  private packagePlanRepo: PackagePlanRepo;

  @BindRepo(PackagePlanDetailRepo)
  private PackagePlanDetailRepo: PackagePlanDetailRepo;

  async findAll() {
    const details = await this.PackagePlanDetailRepo.find({ where: { billingCycle: 1 } });
    const packages = await this.packagePlanRepo.find({ where: { status: NSPackage.EStatus.ACTIVE } });
    return packages.map((pkg) => {
      const detail = details.find((p) => p.packagePlanId === pkg.id);
      return {
        ...pkg,
        detail,
      };
    });
  }

  async findPagination(body: PackagePlanListDto) {
    const { name, status, packagePlanId, pageIndex, pageSize } = body;
    const where: any = { status: NSPackage.EStatus.ACTIVE }
    if (name) {
      where.name = name;
    }
    if (status) {
      where.status = status;
    }
    if (packagePlanId) {
      where.packagePlanId = packagePlanId;
    }
    return await this.packagePlanRepo.findPagination({ where }, { pageIndex, pageSize });
  }
}
