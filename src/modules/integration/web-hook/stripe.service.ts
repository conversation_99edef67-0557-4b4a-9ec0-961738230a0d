import { HttpStatus, Injectable, Req, Res } from '@nestjs/common';
import { Request, Response } from 'express';
import { BindRepo, DefTransaction } from '~/@core/decorator';
import {
  OrderRepo,
  PaymentTransactionRepo,
  PackagePlanRepo,
  MemberPackageRepo,
  MemberPackageHistoryRepo,
  ConfigApiRepo,
  MemberKeyRepo,
  ConfigApiEntity,
  OrderItemRepo,
} from '~/domains/primary';
import Stripe from 'stripe';
import { configEnv } from '~/@config/env';
import { NSOrder } from '~/common/enums/order.enum';
import { NSPackage } from '~/common/enums/package.enum';
import { NSPayment } from '~/common/enums/payment.enum';
import * as dayjs from 'dayjs';
import { StripeWebhookDto } from './dto/stripe.dto';
import { NSConfig, NSMember, NSMemberKey } from '~/common/enums';
import { BusinessException } from '~/@systems/exceptions';
import { PackagePlanDetailRepo } from '~/domains/primary/package-plan-detail/package-plan-detail.repo';

@Injectable()
export class StripeService {
  private stripe: Stripe;
  constructor() {
    this.stripe = new Stripe(configEnv().STRIPE_SECRET_KEY, {});
  }

  @BindRepo(OrderRepo)
  orderRepo: OrderRepo;

  @BindRepo(OrderItemRepo)
  orderItemRepo: OrderItemRepo;

  @BindRepo(PaymentTransactionRepo)
  paymentTransactionRepo: PaymentTransactionRepo;

  @BindRepo(MemberPackageRepo)
  private memberPackageRepo: MemberPackageRepo;

  @BindRepo(PackagePlanRepo)
  private packagePlanRepo: PackagePlanRepo;

  @BindRepo(MemberPackageHistoryRepo)
  private memberPackageHistoryRepo: MemberPackageHistoryRepo;

  @BindRepo(ConfigApiRepo)
  private configApiRepo: ConfigApiRepo;

  @BindRepo(MemberKeyRepo)
  private memberKeyRepo: MemberKeyRepo;

  @BindRepo(PackagePlanDetailRepo)
  private PackagePlanDetailRepo: PackagePlanDetailRepo;

  @DefTransaction()
  async handleWebhook(@Req() req: Request, @Res() res: Response) {
    const sig = req.headers['stripe-signature'];
    if (!sig) {
      return res.status(HttpStatus.BAD_REQUEST).send('No signature found');
    }

    const webhookSecret = configEnv().STRIPE_WEBHOOK_SECRET;
    let event: Stripe.Event;
    try {
      event = this.stripe.webhooks.constructEvent(
        req.body, // ⚠️ Raw body
        sig as string,
        webhookSecret,
      );
    } catch (err) {
      console.error('Webhook signature verification failed.', err.message);
      return res.status(HttpStatus.BAD_REQUEST).send(`Webhook Error: ${err.message}`);
    }

    const session = event.data.object as Stripe.Checkout.Session;
    const metadata = session.metadata;
    const invoiceId = session.invoice as string;

    // Nếu không có metadata thì dừng lại
    if (metadata) {
      const { memberPackageId, orderId, transactionId, timeRegister, paymentType } = metadata;
      const subscriptionId = session.subscription as string;

      switch (event.type) {
        case 'checkout.session.completed':
        case 'checkout.session.async_payment_succeeded':
          if (metadata.memberId && metadata.memberId != null) {
            const { memberId, newPlanId, oldPlanId, transactionType } = metadata;
            const oldPlan = await this.packagePlanRepo.findOne({
              where: { id: oldPlanId },
            });
            const detailOld = await this.PackagePlanDetailRepo.findOne({
              where: { packagePlanId: oldPlan.id, billingCycle: +timeRegister },
            });
            const newPlan = await this.packagePlanRepo.findOne({
              where: { id: newPlanId },
            });
            const detailNew = await this.PackagePlanDetailRepo.findOne({
              where: { packagePlanId: newPlan.id, billingCycle: +timeRegister },
            });

            const order = await this.orderRepo.findOne({ where: { id: orderId } });

            if (!order) throw new BusinessException('payment_hook.order_not_found');
            if (!newPlan) throw new BusinessException('payment_hook.service_package_not_found');
            const detail = await this.PackagePlanDetailRepo.findOne({
              where: { packagePlanId: newPlan.id, billingCycle: +timeRegister },
            });

            const memberPackage = await this.memberPackageRepo.findOne({
              where: { id: memberPackageId },
            });
            if (!memberPackage) throw new BusinessException('payment_hook.member_package_not_found');

            if (transactionType === NSPayment.ETransactionType.UPGRADE) {
              await this.memberPackageRepo.update(
                {
                  memberId: memberId,
                  status: NSMember.EMemberPackageStatus.ACTIVE,
                },
                {
                  packagePlanId: newPlanId,
                  initialTransactionLimit: detail.transactionLimit,
                  initialConfigLimit: detail.configLimit,
                  projectLimit: detail.projectLimit,
                  currentTransaction: 0,
                  status: 'ACTIVE',
                  isAutoRenewal: metadata.isAutoRenewal == 'true',
                  activatedDate: new Date().toISOString(),
                  nextPaymentDate: dayjs()
                    .add(
                      +timeRegister,
                      'month',
                    )
                    .toISOString(),
                  timeRegister: +timeRegister,
                  byteLimit: detail.byteLimit,
                },
              );
            } else if (transactionType === NSPayment.ETransactionType.DOWNGRADE) {
              const listConfig: ConfigApiEntity[] = await this.configApiRepo.find({
                where: { memberId, status: NSConfig.EStatus.ACTIVE },
              });
              for (const config of listConfig) {
                await this.configApiRepo.update(
                  {
                    id: config.id,
                  },
                  {
                    status: NSConfig.EStatus.INACTIVE,
                  },
                );
              }

              const listMemberKey = await this.memberKeyRepo.find({
                where: { memberId, status: NSMemberKey.EStatus.ACTIVE },
              });
              for (const memberKey of listMemberKey) {
                await this.memberKeyRepo.update(
                  {
                    id: memberKey.id,
                  },
                  {
                    status: NSMemberKey.EStatus.INACTIVE,
                  },
                );
              }

              await this.memberPackageRepo.update(
                {
                  id: memberPackageId,
                  status: NSMember.EMemberPackageStatus.ACTIVE,
                },
                {
                  packagePlanId: newPlanId,
                  initialTransactionLimit: detail.transactionLimit,
                  initialConfigLimit: detail.configLimit,
                  projectLimit: detail.projectLimit,
                  currentTransaction: 0,
                  currentConfig: listConfig.filter(item => item.status == NSConfig.EStatus.ACTIVE)
                    .length,
                  currentProject: listMemberKey.filter(
                    item => item.status == NSMemberKey.EStatus.ACTIVE,
                  ).length,
                  status: 'ACTIVE',
                  isAutoRenewal: metadata.isAutoRenewal == 'true',
                  activatedDate: new Date().toISOString(),
                  nextPaymentDate: dayjs()
                    .add(
                      +timeRegister,
                      'month',
                    )
                    .toISOString(),
                  timeRegister: +timeRegister,
                  byteLimit: detail.byteLimit
                },
              );
            } else if (transactionType === NSPayment.ETransactionType.RENEWAL) {
              await this.memberPackageRepo.update(
                {
                  id: memberPackageId,
                  status: NSMember.EMemberPackageStatus.ACTIVE,
                },
                {
                  status: 'ACTIVE',
                  isAutoRenewal: metadata.isAutoRenewal == 'true',
                  nextPaymentDate: dayjs()
                    .add(
                      +timeRegister,
                      'month',
                    )
                    .toISOString(),
                },
              );
            }

            //check orderType ghi lại lịch sử đơn nâng cấp, hạ cấp
            if (transactionType === NSOrder.EOrderType.UPGRADE || transactionType === NSOrder.EOrderType.DOWNGRADE) {
              const history = this.memberPackageHistoryRepo.create({
                memberId: memberId,
                memberPackageId,
                oldPackagePlanId: oldPlanId || null,
                newPackagePlanId: newPlanId || null,
                oldPrice: detailOld.unitPrice,
                newPrice: detailNew.unitPrice,
                newPackageExpiredAt: dayjs().add(1, 'month').toISOString(),
                action: NSMember.EMemberPackageHistoryAction[order.orderType] || 'UPGRADE',
              });
              await this.memberPackageHistoryRepo.save(history);
            }
          }

          await this.updateOrderPaymentStatus({
            orderId,
            paymentTransactionId: transactionId,
            paymentStatus: NSOrder.EPaymentStatus.PAID,
            memberPackageId,
            subscriptionId,
            timeRegister,
            invoiceId,
            invoiceProvider: NSPayment.EPaymentProvider.STRIPE,
          });

          if (event.type === 'checkout.session.completed') {
            const session = event.data.object as Stripe.Checkout.Session;

            if (session.payment_intent && session.customer) {
              const paymentIntent = await this.stripe.paymentIntents.retrieve(
                session.payment_intent as string
              );

              const paymentMethodId = paymentIntent.payment_method as string;

              // Attach payment method nếu chưa được gắn
              await this.stripe.paymentMethods.attach(paymentMethodId, {
                customer: session.customer as string,
              });

              // Gán làm default cho invoice
              await this.stripe.customers.update(session.customer as string, {
                invoice_settings: {
                  default_payment_method: paymentMethodId,
                },
              });
            }
          }
          break;

        case 'checkout.session.async_payment_failed':
          await this.updateOrderPaymentStatus({
            orderId,
            paymentTransactionId: transactionId,
            paymentStatus: NSOrder.EPaymentStatus.UNPAID,
            memberPackageId,
            timeRegister,
            subscriptionId,
          });
          break;

        case 'invoice.paid':
          await this.handleAutoRenewalFromInvoice(event);
          break;
        default:
          return res.status(HttpStatus.OK).send({ message: 'Ignored unhandled event' });
      }

      return res.status(HttpStatus.OK).send({
        message: 'Checkout session event processed successfully',
        data: metadata,
      });
    }
    return res.status(HttpStatus.OK).send({
      message: 'Checkout session event processed successfully',
    });
  }

  async updateOrderPaymentStatus(body: StripeWebhookDto) {
    const {
      orderId,
      paymentTransactionId,
      paymentStatus,
      memberPackageId,
      timeRegister,
      subscriptionId,
      invoiceId,
      invoiceProvider,
    } = body;

    await this.orderRepo.update(
      {
        id: orderId,
      },
      {
        paymentStatus,
        status:
          paymentStatus === NSOrder.EPaymentStatus.PAID
            ? NSOrder.EStatus.COMPLETED
            : NSOrder.EStatus.PENDING,
        paymentDate: new Date().toISOString(),
        invoiceId,
        invoiceProvider,
      },
    );
    await this.paymentTransactionRepo.update(
      {
        id: paymentTransactionId,
      },
      {
        status:
          paymentStatus === NSOrder.EPaymentStatus.PAID
            ? NSPayment.ETransactionStatus.COMPLETED
            : NSPayment.ETransactionStatus.FAILED,
        transactionDate: new Date().toISOString(),
      },
    );

    if (paymentStatus === NSOrder.EPaymentStatus.PAID) {
      const pkg = await this.memberPackageRepo.findOne({ where: { id: memberPackageId } });
      if (!pkg) throw new BusinessException('payment_hook.member_package_not_found');

      const plan = await this.packagePlanRepo.findOne({ where: { id: pkg.packagePlanId } });
      if (!plan) throw new BusinessException('payment_hook.service_package_not_found');


      let expiredDate = null;

      const order = await this.orderRepo.findOne({ where: { id: orderId } });

      if (order.orderType === NSOrder.EOrderType.RENEWAL) {
        expiredDate = dayjs(pkg.expiredDate).add(1, 'month').toISOString();
      } else {
        expiredDate = dayjs().add(+timeRegister, 'month').toISOString();
      }
      const memberPackage = await this.memberPackageRepo.findOne({
        where: { id: memberPackageId },
      });
      await this.memberPackageRepo.update(
        {
          id: memberPackageId,
        },
        {
          subscriptionId: subscriptionId ? subscriptionId : memberPackage.subscriptionId,
          status: NSMember.EMemberPackageStatus.ACTIVE,
          expiredDate,
          activatedDate: new Date().toISOString(),
        },
      );
    }
  }

  // Xử lý tự động gia hạn, tạo giao dịch cho memberPackage khi stripe tự động thanh toán
  async handleAutoRenewalFromInvoice(event: Stripe.Event) {
    const invoice: any = event.data.object as Stripe.Invoice;
    const subscriptionId =
      invoice.subscription ??
      (invoice as any)?.parent?.subscription_details?.subscription;
    if (invoice.billing_reason === 'subscription_create') {
      return;
    }
    const invoiceAmountVat = invoice.total_tax_amounts?.reduce((sum, tax) => sum + +tax.amount, 0) || 0; // convert string to number

    if (!subscriptionId) return;
    const { memberId, orderId, memberPackageId, paymentType, timeRegister } = invoice.parent.subscription_details?.metadata;
    const unit = paymentType === NSPackage.EPlanTypePayment.MONTHLY ? 'month' : 'year';
    const billingPeriod = timeRegister ? +timeRegister : 1;

    await this.memberPackageRepo.update(
      {
        id: memberPackageId,
        subscriptionId
      },
      {
        expiredDate: dayjs().add(billingPeriod, unit).format('YYYY-MM-DD'),
        nextPaymentDate: dayjs().add(billingPeriod, unit).format('YYYY-MM-DD'),
        activatedDate: new Date().toISOString(),
        status: NSMember.EMemberPackageStatus.ACTIVE,
      },
    );

    const member = await this.memberPackageRepo.findOne({ where: { id: memberPackageId } });

    // Tạo order kiểu gia hạn
    const order = await this.orderRepo.create({
      memberId: member.memberId,
      orderType: NSOrder.EOrderType.RENEWAL,
      totalPrice: Math.round(Number(invoice.amount_paid) / 100),
      totalPriceVat: Math.round(Number(invoice.amount_paid) + invoiceAmountVat) / 100,
      vat: Math.round(invoiceAmountVat) / 100,
      paymentStatus: NSOrder.EPaymentStatus.PAID,
      status: NSOrder.EStatus.COMPLETED,
      paymentDate: new Date().toISOString(),
      invoiceId: invoice.id,
      invoiceProvider: NSPayment.EPaymentProvider.STRIPE,
      periodStart: dayjs.unix(invoice.period_start).toDate(),
      periodEnd: dayjs.unix(invoice.period_end).toDate(),
    });
    const newOrder = await this.orderRepo.save(order);

    const orderItem = await this.orderItemRepo.create({
      orderId: newOrder.id,
      packagePlanId: member.packagePlanId,
      price: Math.round(Number(invoice.amount_paid) / 100),
      quantity: 1,
    });
    await this.orderItemRepo.save(orderItem);

    // Tạo payment transaction
    const paymentTransaction = await this.paymentTransactionRepo.create({
      memberId: member.memberId,
      amount: Math.round(Number(invoice.amount_paid) / 100),
      grossAmount: Math.round(Number(invoice.amount_paid) + invoiceAmountVat) / 100,
      paymentProvider: NSPayment.EPaymentProvider.STRIPE,
      paymentMethod: NSPayment.EPaymentMethod.PAY_CARD,
      status: NSPayment.ETransactionStatus.COMPLETED,
      transactionType: NSPayment.ETransactionType.RENEWAL,
      transactionDate: new Date().toISOString(),
      memberPackageId,
      orderId,
    });
    await this.paymentTransactionRepo.save(paymentTransaction);
    return { message: 'Invoice processed successfully' };
  }
}
